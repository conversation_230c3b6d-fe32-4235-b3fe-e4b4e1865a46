package com.tool.pams.repository.domain.organization.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import com.tool.pams.repository.domain.organization.db.OrganizationDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "OrganizationQueryParamsBO对象", description = "")
public class OrganizationQueryParamsBO extends PageParamsBO<OrganizationDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    private Long id;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "机构类型(内部公司、合作方公司、第三方公司)")
    private String organizationType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "合作状态(有效、终止)")
    private String cooperativeStatus;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<OrganizationDO> queryWrapper() {

        LambdaQueryWrapper<OrganizationDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,OrganizationDO::getId,id);

        query.like(StringUtils.isNotBlank(organizationName),OrganizationDO::getOrganizationName,organizationName);

        query.eq(StringUtils.isNotBlank(organizationType),OrganizationDO::getOrganizationType,organizationType);

        query.eq(StringUtils.isNotBlank(description),OrganizationDO::getDescription,description);

        query.eq(StringUtils.isNotBlank(contactPerson),OrganizationDO::getContactPerson,contactPerson);

        query.eq(StringUtils.isNotBlank(contactPhone),OrganizationDO::getContactPhone,contactPhone);

        query.eq(StringUtils.isNotBlank(contactEmail),OrganizationDO::getContactEmail,contactEmail);

        query.eq(StringUtils.isNotBlank(cooperativeStatus),OrganizationDO::getCooperativeStatus,cooperativeStatus);

        query.ge(createTimeStart != null, OrganizationDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, OrganizationDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, OrganizationDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, OrganizationDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),OrganizationDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),OrganizationDO::getUpdater,updater);

        query.eq(deleted!=null,OrganizationDO::getDeleted,deleted);

        return query;
    }
}

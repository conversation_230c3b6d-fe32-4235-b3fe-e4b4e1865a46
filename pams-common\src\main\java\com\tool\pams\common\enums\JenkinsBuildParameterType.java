package com.tool.pams.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @className: JenkinsParamterType
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description: 枚举类，用于表示不同的参数标签
 * @date: 2025/3/3 16:53
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum JenkinsBuildParameterType {
    /**
     * 字符参数
     */
    STR("hudson.model.StringParameterValue", "STRING"),
    /**
     * 选项参数
     */
    CHOICE("hudson.model.ChoiceParameterValue", "CHOICE"),

    /**
     * 额外选项参数
     **/
    
    EXTENDED_CHOICE("com.cwctravel.hudson.plugins.extended_choice_parameter.ExtendedChoiceParameterValue", "EXTENDED"),
    
    /**
     * 文本参数
     */
    TEXT("hudson.model.TextParameterValue", "TEXT"),

    /**
     * 密码参数
     */
    PASSWORD("hudson.model.PasswordParameterValue", "PASSWORD"),

    /**
     * 凭证参数
     */
    CREDENTIALS("com.cloudbees.plugins.credentials.CredentialsParameterValue", "CREDENTIALS"),

    /**
     * 布尔参数
     */
    BOOLEAN("hudson.model.BooleanParameterValue", "BOOLEAN"),

    /**
     * 文件参数
     */
    FILE("hudson.model.FileParameterValue", "FILE"),

    /**
     * 运行参数
     */
    RUN("hudson.model.RunParameterValue", "RUN");

    /**
     * jobxml中的tag
     */
    private final String tag;

    /**
     * 类型标识
     */
    private final String type;


    public static String getType(String tag) {
        for (JenkinsBuildParameterType jenkinsParameterType : values()) {
            if (jenkinsParameterType.getTag().equals(tag)) {
                return jenkinsParameterType.getType();
            }
        }
        throw new IllegalArgumentException("Invalid Jenkins Job Parameter Tag: " + tag);
    }

    public static String getTag(String type) {
        for (JenkinsBuildParameterType jenkinsParameterType : values()) {
            if (jenkinsParameterType.getType().equals(type)) {
                return jenkinsParameterType.getTag();
            }
        }
        throw new IllegalArgumentException("Invalid Jenkins Job Parameter Type: " + type);
    }

    public static boolean containsTag(String tag) {
        return Arrays.stream(values())
                .anyMatch(myEnum -> myEnum.getTag().equals(tag));
    }
}

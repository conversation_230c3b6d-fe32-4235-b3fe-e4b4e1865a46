package com.tool.pams.repository.domain.organization.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "OrganizationUpdateBO对象", description = "")
public class OrganizationUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    private Long id;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "机构类型(内部公司、合作方公司、第三方公司)")
    private String organizationType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "合作状态(有效、终止)")
    private String cooperativeStatus;

}

package com.tool.pams.repository.domain.cmdb.db;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 发布管理与模块关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08 14:42:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_jenkins_publish_module_relation")
@Schema(name = "PamsJenkinsPublishModuleRelationDO对象", description = "发布管理与模块关联表")
public class PamsJenkinsPublishModuleRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一ID")
    @TableId("`id`")
    private Long id;

    @Schema(description = "应用发布ID")
    @TableField("`publish_id`")
    private Long publishId;

    @Schema(description = "应用id")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "构建ID")
    @TableField("`build_id`")
    private String buildId;

    @Schema(description = "模块代号")
    @TableField("`module_code`")
    private String moduleCode;

    @Schema(description = "构建时间（s）")
    @TableField("`duration`")
    private Long duration;


}

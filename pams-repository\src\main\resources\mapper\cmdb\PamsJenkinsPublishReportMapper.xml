<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="sub_app_name" property="subAppName" />
        <result column="branch_name" property="branchName" />
        <result column="publisher" property="publisher" />
        <result column="publish_time" property="publishTime" />
        <result column="handler" property="handler" />
        <result column="dd_approval_num" property="ddApprovalNum" />
        <result column="build_id" property="buildId" />
        <result column="build_content_change" property="buildContentChange" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
        <result column="duration" property="duration" />
        <result column="job_parameter" property="jobParameter" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, app_name, sub_app_name, branch_name, publisher, publish_time, handler, dd_approval_num, build_id, build_content_change, create_time, update_time, creator, updater, deleted, duration, job_parameter
    </sql>

    <select id="syncCmdbPublishRecord" resultType="com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO">
        SELECT
        jpr.id,
        jpr.create_time,
        jpr.update_time,
        jpr.is_delete as deleted,
        jpr.app_id,
        jpr.app_name,
        jpr.sub_app_name,
        jpr.branch_name,
        jpr.build_id,
        jpr.build_content_change,
        jpr.publisher,
        jpr.publish_time,
        jpr.HANDLER,
        jpr.dd_approval_num
        FROM
        `t_cmdb_jenkins_publish_report` jpr
        <where>
            <if test="startTime != null">
                AND jpr.publish_time &gt; #{startTime}
            </if>
            <if test="endTime != null">
                AND jpr.publish_time &lt; #{endTime}
            </if>
        </where>
        ORDER BY jpr.publish_time DESC
    </select>
    
    <insert id="insertOrUpdateBatch">
        INSERT INTO t_pams_jenkins_publish_report (
            id, app_id, app_name, sub_app_name, branch_name, publisher, publish_time, handler,
            dd_approval_num, build_id, build_content_change, create_time, update_time, creator, updater, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.appId}, #{record.appName}, #{record.subAppName}, #{record.branchName},
                #{record.publisher}, #{record.publishTime}, #{record.handler}, #{record.ddApprovalNum},
                #{record.buildId}, #{record.buildContentChange}, #{record.createTime}, #{record.updateTime},
                #{record.creator}, #{record.updater}, #{record.deleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            app_id = VALUES(app_id),
            app_name = VALUES(app_name),
            sub_app_name = VALUES(sub_app_name),
            branch_name = VALUES(branch_name),
            publisher = VALUES(publisher),
            publish_time = VALUES(publish_time),
            handler = VALUES(handler),
            dd_approval_num = VALUES(dd_approval_num),
            build_id = VALUES(build_id),
            build_content_change = VALUES(build_content_change),
            update_time = VALUES(update_time),
            updater = VALUES(updater),
            deleted = VALUES(deleted)
    </insert>

</mapper>

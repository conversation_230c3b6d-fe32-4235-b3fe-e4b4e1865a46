package com.tool.pams.business.service.repository.organization.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.pams.repository.domain.organization.bo.OrganizationSaveBO;
import com.tool.pams.repository.domain.organization.bo.OrganizationUpdateBO;
import com.tool.pams.repository.domain.organization.bo.OrganizationQueryParamsBO;
import com.tool.pams.repository.domain.organization.vo.OrganizationDetailVO;
import com.tool.pams.repository.domain.organization.vo.OrganizationPageVO;
import com.tool.pams.repository.domain.organization.db.OrganizationDO;
import com.tool.pams.repository.domain.common.vo.DropdownOptionVO;
import com.tool.pams.repository.mapper.organization.OrganizationMapper;
import com.tool.pams.business.service.repository.organization.OrganizationService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
@Slf4j
@Service
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationDO> implements OrganizationService {

    @Resource
    private OrganizationMapper organizationMapper;

    @Override
    public Boolean saveInfo(OrganizationSaveBO saveBO){
        OrganizationDO entity = new OrganizationDO();
        BeanUtils.copyProperties(saveBO, entity);
        if (checkOrganizationNameUnique(entity)) {
            throw new ServiceException("机构名称已存在");
        }
        return SqlHelper.retBool(organizationMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(organizationMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(OrganizationUpdateBO updateBO){
        OrganizationDO entity = new OrganizationDO();
        BeanUtils.copyProperties(updateBO, entity);
        if (checkOrganizationNameUnique(entity)) {
            throw new ServiceException("机构名称已存在");
        }
        return SqlHelper.retBool(organizationMapper.updateById(entity));
    }

    @Override
    public OrganizationDetailVO getInfo(Long id){
        OrganizationDO entity = organizationMapper.selectById(id);
        return OrganizationDetailVO.of(entity);
    }

    @Override
    public IPage<OrganizationPageVO> getPageInfo(OrganizationQueryParamsBO queryParamsBO){
        return organizationMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(OrganizationPageVO::of);
    }

    /**
     * 校验机构名称唯一性
     * @param entity
     * @return
     */
    public boolean checkOrganizationNameUnique(OrganizationDO entity) {
        return baseMapper.exists(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getOrganizationName, entity.getOrganizationName())
                .ne(ObjectUtil.isNotNull(entity.getId()), OrganizationDO::getId, entity.getId()));
    }

    @Override
    public List<DropdownOptionVO> getOrganizationDropdownOptions() {
        List<OrganizationDO> organizationList = organizationMapper.selectList(
            new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getDeleted, false)
                .orderByAsc(OrganizationDO::getOrganizationName)
        );

        return organizationList.stream()
            .map(org -> DropdownOptionVO.builder()
                .value(org.getId())
                .label(org.getOrganizationName())
                .build())
            .collect(Collectors.toList());
    }

}

package com.tool.pams.repository.domain.cmdb.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 发布报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08 15:55:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsJenkinsPublishReportPageVO对象", description = "发布报告表")
public class PamsJenkinsPublishReportPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "唯一ID")
    private Long id;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "应用名")
    private String appName;

    @Schema(description = "子应用名")
    private String subAppName;

    @Schema(description = "构建分支")
    private String branchName;

    @Schema(description = "发布人")
    private String publisher;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "钉钉审批单号")
    private String ddApprovalNum;

    @Schema(description = "构建ID")
    private String buildId;

    @Schema(description = "构建内容变化")
    private String buildContentChange;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Schema(description = "构建时间")
    private Long duration;

    @Schema(description = "构建参数")
    private String jobParameter;

    public static PamsJenkinsPublishReportPageVO of(PamsJenkinsPublishReportDO entity){
        if(entity == null){
            return null;
        }
        PamsJenkinsPublishReportPageVO pageVO = new PamsJenkinsPublishReportPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}

package com.tool.pams.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.tool.pams.business.service.repository.system.SysDictValueService;
import com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.tool.pams.repository.domain.system.db.SysDictValueDO;
import com.tool.pams.repository.mapper.cmdb.PamsAppSummaryInfoMapper;
import com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishReportMapper;
import com.tool.pams.repository.mapper.cmdb.PamsSystemAppInfoMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步应用汇总信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncAppSummaryService {

    @Resource
    private PamsAppSummaryInfoMapper pamsAppSummaryInfoMapper;

    @Resource
    private PamsSystemAppInfoMapper pamsSystemAppInfoMapper;

    @Resource
    private PamsJenkinsPublishReportMapper pamsJenkinsPublishReportMapper;

    @Resource
    private SysDictValueService sysDictValueService;

    /**
     * 同步应用汇总信息
     */
    @TraceId("同步应用汇总信息")
    @XxlJob("syncAppSummaryHandler")
    public ReturnT<String> syncAppSummaryHandler() {
        XxlJobLogger.log("同步应用汇总信息开始. traceId:{}", MdcUtil.getTrace());
        ReturnT<String> returnT = ReturnT.SUCCESS;

        try {
            // 1. 查询所有父级应用
            List<PamsSystemAppInfoDO> parentApps = pamsSystemAppInfoMapper.selectList(
                new LambdaQueryWrapper<PamsSystemAppInfoDO>()
                    .eq(PamsSystemAppInfoDO::getParentAppId, 0L)
                    .eq(PamsSystemAppInfoDO::getDeleted, false)
            );

            if (CollectionUtil.isEmpty(parentApps)) {
                XxlJobLogger.log("没有找到父级应用，同步结束");
                return returnT;
            }

            XxlJobLogger.log("找到 {} 个父级应用需要同步", parentApps.size());

            List<PamsAppSummaryInfoDO> summaryInfoList = new ArrayList<>();

            for (PamsSystemAppInfoDO parentApp : parentApps) {
                PamsAppSummaryInfoDO summaryInfo = buildAppSummaryInfo(parentApp);
                if (summaryInfo != null) {
                    summaryInfoList.add(summaryInfo);
                }
            }

            // 2. 批量插入或更新（使用定时器专用方法，只更新计算字段）
            if (CollectionUtil.isNotEmpty(summaryInfoList)) {
                pamsAppSummaryInfoMapper.insertOrUpdateBatchForScheduler(summaryInfoList);
                XxlJobLogger.log("成功同步 {} 个应用汇总信息", summaryInfoList.size());
            }

            // 3. 处理已删除的应用
            handleDeletedApps();

        } catch (Exception e) {
            log.error("同步应用汇总信息异常：{}", e.getMessage(), e);
            XxlJobLogger.log("同步应用汇总信息异常：{}", e.getMessage());
            returnT = ReturnT.FAIL;
        }

        XxlJobLogger.log("同步应用汇总信息结束");
        return returnT;
    }

    /**
     * 构建应用汇总信息
     */
    private PamsAppSummaryInfoDO buildAppSummaryInfo(PamsSystemAppInfoDO parentApp) {
        Long appId = parentApp.getId();
        
        // 1. 计算模块数量
        Integer moduleCount = Math.toIntExact(pamsSystemAppInfoMapper.selectCount(
                new LambdaQueryWrapper<PamsSystemAppInfoDO>()
                        .eq(PamsSystemAppInfoDO::getParentAppId, appId)
                        .eq(PamsSystemAppInfoDO::getDeleted, false)
        ));
        
        // 2. 计算发布统计信息
        PublishStats publishStats = calculatePublishStats(appId);

        // 3. 构建汇总信息对象（只包含定时器需要更新的计算字段）
        return PamsAppSummaryInfoDO.builder()
            .appId(appId)
            .appName(parentApp.getAppName())
            // 不设置status、type、organizationId等手动填写字段
            .moduleCount(moduleCount)
            .totalPublishCount(publishStats.getTotalPublishCount())
            .totalPublishDuration(publishStats.getTotalPublishDuration())
            .avgPublishDuration(publishStats.getAvgPublishDuration())
            .avgStartupDuration(publishStats.getAvgStartupDuration())
            .devTeam(getDevTeamByDirector(parentApp.getDevelopDirector()))
            .prodAppIdentifier(parentApp.getAppCode())
            .gitAddress(parentApp.getGitlabAddress())
            .developLanguage(parentApp.getDevelopLanguage())
            .deleted(parentApp.getDeleted())
            .build();
    }

    /**
     * 计算发布统计信息
     */
    private PublishStats calculatePublishStats(Long appId) {
        // 查询该应用的所有发布记录
        List<PamsJenkinsPublishReportDO> publishRecords = pamsJenkinsPublishReportMapper.selectList(
            new LambdaQueryWrapper<PamsJenkinsPublishReportDO>()
                .eq(PamsJenkinsPublishReportDO::getAppId, appId)
                .eq(PamsJenkinsPublishReportDO::getDeleted, false)
        );

        if (CollectionUtil.isEmpty(publishRecords)) {
            return new PublishStats(0, 0L, 0L, 0L);
        }

        // 按build_id去重计算总发布次数和总发布时长
        Map<String, PamsJenkinsPublishReportDO> uniqueBuilds = publishRecords.stream()
            .filter(record -> record.getBuildId() != null)
            .collect(Collectors.toMap(
                PamsJenkinsPublishReportDO::getBuildId,
                record -> record,
                (existing, replacement) -> existing
            ));

        int totalPublishCount = uniqueBuilds.size();

        // 计算总发布时长（按build_id去重后的duration总和）
        long totalPublishDuration = uniqueBuilds.values().stream()
            .mapToLong(record -> ObjectUtil.defaultIfNull(record.getDuration(), 0L))
            .sum();

        // 计算平均发布时长
        long avgPublishDuration = totalPublishCount > 0 ? totalPublishDuration / totalPublishCount : 0L;

        // 计算平均启动时长（所有记录的duration总和除以总记录数，不去重）
        long totalStartupDuration = publishRecords.stream()
            .mapToLong(record -> ObjectUtil.defaultIfNull(record.getDuration(), 0L))
            .sum();
        long avgStartupDuration = publishRecords.size() > 0 ? totalStartupDuration / publishRecords.size() : 0L;

        return new PublishStats(totalPublishCount, totalPublishDuration, avgPublishDuration, avgStartupDuration);
    }



    /**
     * 根据开发负责人获取开发组
     */
    private String getDevTeamByDirector(String developDirector) {
        if (developDirector == null || developDirector.trim().isEmpty()) {
            return null;
        }

        // 这里可以根据字典表来查询开发组信息
        // 假设字典表中有一个keyName为"dev_team_mapping"的配置
        List<SysDictValueDO> dictValues = sysDictValueService.listByKeyName("dev_team_mapping");

        // 查找匹配的开发组
        for (SysDictValueDO dictValue : dictValues) {
            if (developDirector.equals(dictValue.getValue())) {
                return dictValue.getLabel();
            }
        }

        // 如果没有找到匹配的，直接返回开发负责人
        return developDirector;
    }

    /**
     * 处理已删除的应用
     */
    private void handleDeletedApps() {
        // 查询已删除的父级应用
        List<PamsSystemAppInfoDO> deletedApps = pamsSystemAppInfoMapper.selectList(
            new LambdaQueryWrapper<PamsSystemAppInfoDO>()
                .eq(PamsSystemAppInfoDO::getParentAppId, 0L)
                .eq(PamsSystemAppInfoDO::getDeleted, true)
        );

        if (CollectionUtil.isNotEmpty(deletedApps)) {
            List<Long> deletedAppIds = deletedApps.stream()
                .map(PamsSystemAppInfoDO::getId)
                .collect(Collectors.toList());

            // 将对应的汇总信息也标记为删除
            List<PamsAppSummaryInfoDO> summaryToDelete = pamsAppSummaryInfoMapper.selectList(
                new LambdaQueryWrapper<PamsAppSummaryInfoDO>()
                    .in(PamsAppSummaryInfoDO::getAppId, deletedAppIds)
                    .eq(PamsAppSummaryInfoDO::getDeleted, false)
            );

            if (CollectionUtil.isNotEmpty(summaryToDelete)) {
                summaryToDelete.forEach(summary -> summary.setDeleted(true));
                pamsAppSummaryInfoMapper.insertOrUpdateBatchForScheduler(summaryToDelete);
                XxlJobLogger.log("标记删除 {} 个应用汇总信息", summaryToDelete.size());
            }
        }
    }


    /**
     * 发布统计信息内部类
     */
    private static class PublishStats {
        private final Integer totalPublishCount;
        private final Long totalPublishDuration;
        private final Long avgPublishDuration;
        private final Long avgStartupDuration;

        public PublishStats(Integer totalPublishCount, Long totalPublishDuration, 
                          Long avgPublishDuration, Long avgStartupDuration) {
            this.totalPublishCount = totalPublishCount;
            this.totalPublishDuration = totalPublishDuration;
            this.avgPublishDuration = avgPublishDuration;
            this.avgStartupDuration = avgStartupDuration;
        }

        public Integer getTotalPublishCount() { return totalPublishCount; }
        public Long getTotalPublishDuration() { return totalPublishDuration; }
        public Long getAvgPublishDuration() { return avgPublishDuration; }
        public Long getAvgStartupDuration() { return avgStartupDuration; }
    }
}

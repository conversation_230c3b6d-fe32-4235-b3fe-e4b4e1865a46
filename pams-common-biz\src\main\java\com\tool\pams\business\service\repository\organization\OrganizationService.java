package com.tool.pams.business.service.repository.organization;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.organization.db.OrganizationDO;
import com.tool.pams.repository.domain.organization.bo.OrganizationSaveBO;
import com.tool.pams.repository.domain.organization.bo.OrganizationUpdateBO;
import com.tool.pams.repository.domain.organization.bo.OrganizationQueryParamsBO;
import com.tool.pams.repository.domain.organization.vo.OrganizationDetailVO;
import com.tool.pams.repository.domain.organization.vo.OrganizationPageVO;
import com.tool.pams.repository.domain.common.vo.DropdownOptionVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
public interface OrganizationService extends IService<OrganizationDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(OrganizationSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(OrganizationUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    OrganizationDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<OrganizationPageVO> getPageInfo(OrganizationQueryParamsBO queryParamsBO);

    /**
     * 获取机构下拉框选项
     *
     * @return 机构下拉框选项列表
     */
    List<DropdownOptionVO> getOrganizationDropdownOptions();

}

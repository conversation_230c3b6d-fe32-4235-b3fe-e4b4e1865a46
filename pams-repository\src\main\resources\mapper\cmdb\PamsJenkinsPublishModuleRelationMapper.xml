<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishModuleRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishModuleRelationDO">
        <id column="id" property="id" />
        <result column="publish_id" property="publishId" />
        <result column="app_id" property="appId" />
        <result column="build_id" property="buildId" />
        <result column="module_code" property="moduleCode" />
        <result column="duration" property="duration" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, publish_id, app_id, build_id, module_code, duration
    </sql>

</mapper>

package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.PamsJenkinsPublishReportService;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsJenkinsPublishReportQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsJenkinsPublishReportDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsJenkinsPublishReportPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 发布报告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08 17:09:57
 */
@Tag(name = "发布报告表")
@RestController
@RequestMapping("/pamsJenkinsPublishReport")
public class PamsJenkinsPublishReportController {

    @Resource
    private PamsJenkinsPublishReportService pamsJenkinsPublishReportService;

    @ApiResponse
    @Operation(summary = "根据id查询发布报告表")
    @PrintLog("根据id查询发布报告表")
    @GetMapping("/info/{id}")
    public PamsJenkinsPublishReportDetailVO info(@PathVariable("id") Long id) {
        return pamsJenkinsPublishReportService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "分页查询发布报告表")
    @PrintLog("分页查询发布报告表")
    @GetMapping("/page")
    public IPage<PamsJenkinsPublishReportPageVO> page(@Valid @ParameterObject PamsJenkinsPublishReportQueryParamsBO queryParamsBO) {
        return pamsJenkinsPublishReportService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存发布报告表")
    @PrintLog("保存发布报告表")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid PamsJenkinsPublishReportSaveBO saveBO) {
        return pamsJenkinsPublishReportService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改发布报告表")
    @PrintLog("修改发布报告表")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid PamsJenkinsPublishReportUpdateBO updateBO) {
        return pamsJenkinsPublishReportService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除发布报告表")
    @PrintLog("删除发布报告表")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return pamsJenkinsPublishReportService.delInfo(id);
    }
}

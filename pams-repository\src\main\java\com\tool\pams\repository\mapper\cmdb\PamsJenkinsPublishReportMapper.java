package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tool.pams.repository.domain.cmdb.db.PamsJenkinsPublishReportDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发布报告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08 15:55:40
 */
@Mapper
public interface PamsJenkinsPublishReportMapper extends BaseMapper<PamsJenkinsPublishReportDO> {

    /**
     * 同步生产发布
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @DS("cmdb")
    List<PamsJenkinsPublishReportDO> syncCmdbPublishRecord(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量插入或更新Jenkins发布报告（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records Jenkins发布报告列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<PamsJenkinsPublishReportDO> records);

}

package com.tool.pams.repository.domain.organization.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_organization")
@Schema(name = "OrganizationDO对象", description = "")
public class OrganizationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("`id`")
    private Long id;

    @Schema(description = "机构名称")
    @TableField("`organization_name`")
    private String organizationName;

    @Schema(description = "机构类型(内部公司、合作方公司、第三方公司)")
    @TableField("`organization_type`")
    private String organizationType;

    @Schema(description = "描述")
    @TableField("`description`")
    private String description;

    @Schema(description = "联系人")
    @TableField("`contact_person`")
    private String contactPerson;

    @Schema(description = "联系电话")
    @TableField("`contact_phone`")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    @TableField("`contact_email`")
    private String contactEmail;

    @Schema(description = "合作状态(有效、终止)")
    @TableField("`cooperative_status`")
    private String cooperativeStatus;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}

package com.tool.pams.repository.domain.cmdb.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 部门和人员请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08 14:20:27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "DeptAndMemberDO对象", description = "字典获取部门和成员")
public class DeptAndMemberBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "成员名称")
    private String member;


}

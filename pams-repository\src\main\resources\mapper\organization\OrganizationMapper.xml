<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.organization.OrganizationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.organization.db.OrganizationDO">
        <id column="id" property="id" />
        <result column="organization_name" property="organizationName" />
        <result column="organization_type" property="organizationType" />
        <result column="description" property="description" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="contact_email" property="contactEmail" />
        <result column="cooperative_status" property="cooperativeStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, organization_name, organization_type, description, contact_person, contact_phone, contact_email, cooperative_status, create_time, update_time, creator, updater, deleted
    </sql>

</mapper>

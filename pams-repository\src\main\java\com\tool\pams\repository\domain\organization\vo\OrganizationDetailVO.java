package com.tool.pams.repository.domain.organization.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.pams.repository.domain.organization.db.OrganizationDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "OrganizationDetailVO对象", description = "")
public class OrganizationDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    private Long id;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "机构类型(内部公司、合作方公司、第三方公司)")
    private String organizationType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "合作状态(有效、终止)")
    private String cooperativeStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static OrganizationDetailVO of(OrganizationDO entity){
        if(entity == null){
            return null;
        }
        OrganizationDetailVO detailVO = new OrganizationDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}

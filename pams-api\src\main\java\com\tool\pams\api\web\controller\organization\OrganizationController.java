package com.tool.pams.api.web.controller.organization;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.pams.repository.domain.common.vo.DropdownOptionVO;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.organization.OrganizationService;
import com.tool.pams.repository.domain.organization.bo.OrganizationSaveBO;
import com.tool.pams.repository.domain.organization.bo.OrganizationUpdateBO;
import com.tool.pams.repository.domain.organization.bo.OrganizationQueryParamsBO;
import com.tool.pams.repository.domain.organization.vo.OrganizationDetailVO;
import com.tool.pams.repository.domain.organization.vo.OrganizationPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 15:41:40
 */
@Tag(name = "")
@RestController
@RequestMapping("/organization")
public class OrganizationController {

    @Resource
    private OrganizationService organizationService;

    @ApiResponse
    @Operation(summary = "根据id查询")
    @PrintLog("根据id查询")
    @GetMapping("/info/{id}")
    public OrganizationDetailVO info(@PathVariable("id") Long id) {
        return organizationService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "分页查询")
    @PrintLog("分页查询")
    @GetMapping("/page")
    public IPage<OrganizationPageVO> page(@Valid @ParameterObject OrganizationQueryParamsBO queryParamsBO) {
        return organizationService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存")
    @PrintLog("保存")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid OrganizationSaveBO saveBO) {
        return organizationService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改")
    @PrintLog("修改")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid OrganizationUpdateBO updateBO) {
        return organizationService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除")
    @PrintLog("删除")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return organizationService.delInfo(id);
    }

    @ApiResponse
    @Operation(summary = "获取机构下拉框选项")
    @PrintLog("获取机构下拉框选项")
    @GetMapping("/list")
    public List<DropdownOptionVO> getOrganizationDropdownOptions() {
        return organizationService.getOrganizationDropdownOptions();
    }
}

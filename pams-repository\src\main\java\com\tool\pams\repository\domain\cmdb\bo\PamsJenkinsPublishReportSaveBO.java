package com.tool.pams.repository.domain.cmdb.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 发布报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08 15:55:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsJenkinsPublishReportSaveBO对象", description = "发布报告表")
public class PamsJenkinsPublishReportSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "应用ID")
    private Long appId;


    @Schema(description = "应用名")
    private String appName;


    @Schema(description = "子应用名")
    private String subAppName;


    @Schema(description = "构建分支")
    private String branchName;


    @Schema(description = "发布人")
    private String publisher;


    @Schema(description = "发布时间")
    private LocalDateTime publishTime;


    @Schema(description = "处理人")
    private String handler;


    @Schema(description = "钉钉审批单号")
    private String ddApprovalNum;


    @Schema(description = "构建ID")
    private String buildId;


    @Schema(description = "构建内容变化")
    private String buildContentChange;


    @Schema(description = "构建时间")
    private Long duration;


    @Schema(description = "构建参数")
    private String jobParameter;


}

package com.tool.pams.repository.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tool.pams.repository.domain.cmdb.bo.DeptAndMemberBO;
import com.tool.pams.repository.domain.system.db.DeptAndMemberDO;
import com.tool.pams.repository.domain.system.db.SysDictValueDO;

import java.util.List;

/**
 * <p>
 * 字典配置值 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
public interface SysDictValueMapper extends BaseMapper<SysDictValueDO> {


    /**
     * 获取字典中的部门和人员
     *
     * @param deptAndMemberBO 应用汇总信息列表
     * @return 受影响的行数
     */
    List<DeptAndMemberDO> getDevDeptAndMember(DeptAndMemberBO deptAndMemberBO);
}

package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.pams.repository.domain.common.vo.DropdownOptionVO;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.PamsBusinessSystemInfoService;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 新业务系统资源信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08 17:09:57
 */
@Tag(name = "新业务系统资源信息表")
@RestController
@RequestMapping("/pamsBusinessSystemInfo")
public class PamsBusinessSystemInfoController {

    @Resource
    private PamsBusinessSystemInfoService pamsBusinessSystemInfoService;

    @Operation(summary = "获取产品下拉框选项")
    @PrintLog("获取产品下拉框选项")
    @GetMapping("/list")
    public List<DropdownOptionVO> getProductDropdownOptions() {
        return pamsBusinessSystemInfoService.getProductDropdownOptions();
    }

    @ApiResponse
    @Operation(summary = "根据id查询新业务系统资源信息表")
    @PrintLog("根据id查询新业务系统资源信息表")
    @GetMapping("/info/{id}")
    public PamsBusinessSystemInfoDetailVO info(@PathVariable("id") Long id) {
        return pamsBusinessSystemInfoService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "分页查询新业务系统资源信息表")
    @PrintLog("分页查询新业务系统资源信息表")
    @GetMapping("/page")
    public IPage<PamsBusinessSystemInfoPageVO> page(@Valid @ParameterObject PamsBusinessSystemInfoQueryParamsBO queryParamsBO) {
        return pamsBusinessSystemInfoService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存新业务系统资源信息表")
    @PrintLog("保存新业务系统资源信息表")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid PamsBusinessSystemInfoSaveBO saveBO) {
        return pamsBusinessSystemInfoService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改新业务系统资源信息表")
    @PrintLog("修改新业务系统资源信息表")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid PamsBusinessSystemInfoUpdateBO updateBO) {
        return pamsBusinessSystemInfoService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除新业务系统资源信息表")
    @PrintLog("删除新业务系统资源信息表")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return pamsBusinessSystemInfoService.delInfo(id);
    }
}
